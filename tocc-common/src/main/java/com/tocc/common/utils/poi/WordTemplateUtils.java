package com.tocc.common.utils.poi;

import org.apache.poi.xwpf.usermodel.*;

import java.util.*;

/**
 * Word模板工具类
 */
public class WordTemplateUtils {

    /**
     * 处理文档内容替换
     *
     * @param document
     * @param replacements
     */
    public static void processDocument(XWPFDocument document, Map<String, String> replacements) {
        //处理段落
        document.getParagraphs().forEach(p -> replaceInParagraph(p, replacements));
        //处理表格
        document.getTables().forEach(t -> processTable(t, replacements));
        //处理页眉
        document.getHeaderList().forEach(h ->
                h.getParagraphs().forEach(p -> replaceInParagraph(p, replacements)));
        //处理页脚
        document.getFooterList().forEach(f ->
                f.getParagraphs().forEach(p -> replaceInParagraph(p, replacements)));
    }

    private static void processTable(XWPFTable table, Map<String, String> replacements) {
        table.getRows().forEach(row ->
                row.getTableCells().forEach(cell ->
                        cell.getParagraphs().forEach(p ->
                                replaceInParagraph(p, replacements))));
    }

    private static void replaceInParagraph(XWPFParagraph paragraph, Map<String, String> replacements) {
        String text = paragraph.getText();
        if (text == null || !containsPlaceholder(text, replacements)) {
            return;
        }

        try {
            //执行替换
            String newText = text;
            for (Map.Entry<String, String> entry : replacements.entrySet()) {
                newText = newText.replace(entry.getKey(), entry.getValue());
            }

            //保留样式
            List<XWPFRun> runs = paragraph.getRuns();
            XWPFRun firstRun = runs.isEmpty() ? null : runs.get(0);

            //清除原有内容
            for (int i = runs.size() - 1; i >= 0; i--) {
                paragraph.removeRun(i);
            }

            //添加新内容，支持换行
            if (firstRun != null) {
                addTextWithLineBreaks(paragraph, newText, firstRun);
            } else {
                XWPFRun newRun = paragraph.createRun();
                addTextWithLineBreaks(newRun, newText);
            }
        } catch (Exception e) {
            // 如果样式复制失败，使用简单替换
            simpleReplaceInParagraph(paragraph, replacements);
        }
    }

    /**
     * 简单替换方法，不保留样式
     */
    private static void simpleReplaceInParagraph(XWPFParagraph paragraph, Map<String, String> replacements) {
        try {
            String text = paragraph.getText();
            if (text == null) {
                return;
            }

            //执行替换
            String newText = text;
            for (Map.Entry<String, String> entry : replacements.entrySet()) {
                newText = newText.replace(entry.getKey(), entry.getValue());
            }

            //清除原有内容
            List<XWPFRun> runs = paragraph.getRuns();
            for (int i = runs.size() - 1; i >= 0; i--) {
                paragraph.removeRun(i);
            }

            //添加新内容（不保留样式），支持换行
            XWPFRun newRun = paragraph.createRun();
            addTextWithLineBreaks(newRun, newText);
        } catch (Exception e) {
            // 记录错误但不抛出异常
            System.err.println("替换段落内容时发生错误: " + e.getMessage());
        }
    }

    private static boolean containsPlaceholder(String text, Map<String, String> replacements) {
        return replacements.keySet().stream().anyMatch(text::contains);
    }

    private static void copyRunStyle(XWPFRun source, XWPFRun target) {
        try {
            // 安全地复制样式，避免XML断开连接异常
            if (source.isBold()) {
                target.setBold(true);
            }
        } catch (Exception e) {
            // 忽略粗体设置异常
        }

        try {
            if (source.isItalic()) {
                target.setItalic(true);
            }
        } catch (Exception e) {
            // 忽略斜体设置异常
        }

        try {
            String fontFamily = source.getFontFamily();
            if (fontFamily != null) {
                target.setFontFamily(fontFamily);
            }
        } catch (Exception e) {
            // 忽略字体设置异常
        }

        try {
            int fontSize = source.getFontSize();
            if (fontSize > 0) {
                target.setFontSize(fontSize);
            }
        } catch (Exception e) {
            // 忽略字号设置异常
        }

        try {
            String color = source.getColor();
            if (color != null) {
                target.setColor(color);
            }
        } catch (Exception e) {
            // 忽略颜色设置异常
        }

        try {
            target.setUnderline(source.getUnderline());
        } catch (Exception e) {
            // 忽略下划线设置异常
        }
    }

    /**
     * 添加文本并支持换行
     */
    private static void addTextWithLineBreaks(XWPFRun run, String text) {
        if (text == null || text.isEmpty()) {
            return;
        }

        String[] lines = text.split("\\n");
        for (int i = 0; i < lines.length; i++) {
            if (i > 0) {
                run.addBreak(); // 添加换行
            }
            run.setText(lines[i], i);
        }
    }

    /**
     * 在段落中添加文本并支持换行，保留样式
     */
    private static void addTextWithLineBreaks(XWPFParagraph paragraph, String text, XWPFRun styleSource) {
        if (text == null || text.isEmpty()) {
            return;
        }

        String[] lines = text.split("\\n");
        for (int i = 0; i < lines.length; i++) {
            XWPFRun run = paragraph.createRun();
            copyRunStyle(styleSource, run);
            run.setText(lines[i]);

            if (i < lines.length - 1) {
                // 不是最后一行，添加换行
                run.addBreak();
            }
        }
    }
}