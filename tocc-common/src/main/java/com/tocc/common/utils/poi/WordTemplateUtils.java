package com.tocc.common.utils.poi;

import org.apache.poi.xwpf.usermodel.*;

import java.util.*;

/**
 * Word模板工具类
 */
public class WordTemplateUtils {

    /**
     * 处理文档内容替换
     *
     * @param document
     * @param replacements
     */
    public static void processDocument(XWPFDocument document, Map<String, String> replacements) {
        //处理段落
        document.getParagraphs().forEach(p -> replaceInParagraph(p, replacements));
        //处理表格
        document.getTables().forEach(t -> processTable(t, replacements));
        //处理页眉
        document.getHeaderList().forEach(h ->
                h.getParagraphs().forEach(p -> replaceInParagraph(p, replacements)));
        //处理页脚
        document.getFooterList().forEach(f ->
                f.getParagraphs().forEach(p -> replaceInParagraph(p, replacements)));
    }

    private static void processTable(XWPFTable table, Map<String, String> replacements) {
        table.getRows().forEach(row ->
                row.getTableCells().forEach(cell ->
                        cell.getParagraphs().forEach(p ->
                                replaceInParagraph(p, replacements))));
    }

    private static void replaceInParagraph(XWPFParagraph paragraph, Map<String, String> replacements) {
        String text = paragraph.getText();
        if (text == null || !containsPlaceholder(text, replacements)) {
            return;
        }
        //执行替换
        String newText = text;
        for (Map.Entry<String, String> entry : replacements.entrySet()) {
            newText = newText.replace(entry.getKey(), entry.getValue());
        }
        //保留样式
        List<XWPFRun> runs = paragraph.getRuns();
        XWPFRun firstRun = runs.isEmpty() ? null : runs.get(0);

        //清除原有内容
        for (int i = runs.size() - 1; i >= 0; i--) {
            paragraph.removeRun(i);
        }
        //添加新内容
        XWPFRun newRun = paragraph.createRun();
        if (firstRun != null) {
            copyRunStyle(firstRun, newRun);
        }
        newRun.setText(newText);
    }

    private static boolean containsPlaceholder(String text, Map<String, String> replacements) {
        return replacements.keySet().stream().anyMatch(text::contains);
    }

    private static void copyRunStyle(XWPFRun source, XWPFRun target) {
        target.setBold(source.isBold());
        target.setItalic(source.isItalic());
        target.setFontFamily(source.getFontFamily());
        target.setFontSize(source.getFontSize());
        target.setColor(source.getColor());
        target.setUnderline(source.getUnderline());
    }
}