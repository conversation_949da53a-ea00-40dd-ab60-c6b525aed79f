package com.tocc.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.google.gson.Gson;
import com.tocc.common.core.domain.entity.SysUser;
import com.tocc.common.core.domain.model.LoginUser;
import com.tocc.common.enums.LevelEnum;
import com.tocc.common.exception.ServiceException;
import com.tocc.common.utils.SecurityUtils;
import com.tocc.common.utils.StringUtils;
import com.tocc.common.utils.poi.WordTemplateUtils;
import com.tocc.domain.dto.*;
import com.tocc.domain.vo.EmergencyEventDetailVO;
import com.tocc.domain.vo.EmergencyEventVO;
import com.tocc.domain.vo.YkTokenVO;
import com.tocc.mapper.EmergencyEventMapper;
import com.tocc.mapper.EmergencyEventRoadTrafficMapper;
import com.tocc.mapper.EmergencyEventWaterwayTrafficMapper;
import com.tocc.service.IAlarmService;
import com.tocc.service.IEmergencyEventService;
import com.tocc.system.service.ISysUserService;
import com.tocc.em.service.IEmPrePlanService;
import com.tocc.em.vo.EmPrePlanVO;
import com.tocc.em.dto.EmPrePlanDeptDTO;
import java.util.regex.Pattern;
import java.util.regex.Matcher;
import com.tocc.utils.HttpClientUtils;
import lombok.SneakyThrows;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.apache.poi.xwpf.usermodel.XWPFParagraph;
import org.apache.poi.xwpf.usermodel.XWPFRun;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTP;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTPPr;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTSpacing;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.STLineSpacingRule;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.BufferedInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigInteger;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.MessageFormat;
import java.time.Instant;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * 应急事件Service业务层处理
 *
 * <AUTHOR>
 */
@Service
public class EmergencyEventServiceImpl implements IEmergencyEventService {

    public static final Logger log = LoggerFactory.getLogger(EmergencyEventServiceImpl.class);

    @Autowired
    private EmergencyEventMapper emergencyEventMapper;

    @Autowired
    private EmergencyEventRoadTrafficMapper roadTrafficMapper;

    @Autowired
    private EmergencyEventWaterwayTrafficMapper waterwayTrafficMapper;

    @Autowired
    private IAlarmService alarmService;

    @Autowired
    private ISysUserService userService;

    @Autowired
    private IEmPrePlanService emPrePlanService;

    /**
     * 查询应急事件
     *
     * @param eventId 应急事件主键
     * @return 应急事件
     */
    @Override
    public EmergencyEventVO selectEmergencyEventByEventId(String eventId) {
        EmergencyEventDTO dto = emergencyEventMapper.selectEmergencyEventByEventId(eventId);
        if (dto == null) {
            return null;
        }
        return convertToVO(dto);
    }

    /**
     * 查询应急事件详情（包含扩展信息）
     *
     * @param eventId 应急事件主键
     * @return 应急事件详情
     */
    @Override
    public EmergencyEventDetailVO selectEmergencyEventDetailByEventId(String eventId) {
        EmergencyEventDTO dto = emergencyEventMapper.selectEmergencyEventByEventId(eventId);
        if (dto == null) {
            return null;
        }

        EmergencyEventDetailVO detailVO = new EmergencyEventDetailVO();
        BeanUtils.copyProperties(convertToVO(dto), detailVO);

        // 根据事件类型查询扩展信息
        if ("1".equals(dto.getEventType())) { // 道路交通事故
            EmergencyEventRoadTrafficDTO roadTrafficDTO = roadTrafficMapper.selectEmergencyEventRoadTrafficByEventId(eventId);
            if (roadTrafficDTO != null) {
                detailVO.setRoadSectionCode(roadTrafficDTO.getRoadSectionCode());
                detailVO.setStartStakeNumber(roadTrafficDTO.getStartStakeNumber());
                detailVO.setEndStakeNumber(roadTrafficDTO.getEndStakeNumber());
                detailVO.setDirection(roadTrafficDTO.getDirection());
                detailVO.setTrafficAffected(roadTrafficDTO.getTrafficAffected());
                detailVO.setVehicleType(roadTrafficDTO.getVehicleType());
                detailVO.setEstimatedRecoveryTime(roadTrafficDTO.getEstimatedRecoveryTime());
                detailVO.setRoadCasualtySituation(roadTrafficDTO.getCasualtySituation());
                detailVO.setImpactTrend(roadTrafficDTO.getImpactTrend());
                // TODO: 设置字典名称
            }
        } else if ("2".equals(dto.getEventType())) { // 水路交通事故
            EmergencyEventWaterwayTrafficDTO waterwayTrafficDTO = waterwayTrafficMapper.selectEmergencyEventWaterwayTrafficByEventId(eventId);
            if (waterwayTrafficDTO != null) {
                detailVO.setWaterwayName(waterwayTrafficDTO.getWaterwayName());
                detailVO.setShipName(waterwayTrafficDTO.getShipName());
                detailVO.setShipType(waterwayTrafficDTO.getShipType());
                detailVO.setShipTonnage(waterwayTrafficDTO.getShipTonnage());
                detailVO.setWaterwayCasualtySituation(waterwayTrafficDTO.getCasualtySituation());
                detailVO.setCargoInfo(waterwayTrafficDTO.getCargoInfo());
                detailVO.setEnvironmentalImpact(waterwayTrafficDTO.getEnvironmentalImpact());
                // TODO: 设置字典名称
            }
        }

        return detailVO;
    }

    /**
     * 查询应急事件列表
     *
     * @param emergencyEvent 应急事件
     * @return 应急事件
     */
    @Override
    public List<EmergencyEventVO> selectEmergencyEventList(EmergencyEventDTO emergencyEvent) {
        // 从SecurityUtils获取当前用户ID
        String currentUserId = getCurrentUserId();

        // 创建查询参数，只查询当前用户作为上报人或填报人的事件
        EmergencyEventDTO queryParam = new EmergencyEventDTO();
        // 复制原有查询条件
        BeanUtils.copyProperties(emergencyEvent, queryParam);
        // 设置当前用户ID用于权限过滤
        queryParam.setReporterId(currentUserId);

        // 直接返回包含用户详细信息的VO列表
        return emergencyEventMapper.selectEmergencyEventListByUser(queryParam);
    }

    /**
     * 新增应急事件
     *
     * @param createDTO 应急事件
     * @return 结果
     */
    @Override
    @Transactional
    public int insertEmergencyEvent(EmergencyEventCreateDTO createDTO) {
        // 生成事件ID
        String eventId = UUID.randomUUID().toString();

        // 转换为主表DTO并设置系统字段
        EmergencyEventDTO eventDTO = createDTO.toEmergencyEventDTO();
        eventDTO.setEventId(eventId);
        Long time = System.currentTimeMillis() / 1000;
        eventDTO.setCreateTime(time);
        // 设置创建者ID（从当前登录用户获取）
        String currentUserId = getCurrentUserId();
        eventDTO.setCreaterId(currentUserId);

        // 如果没有指定填报人，使用当前用户
        if (createDTO.getReporterId() == null) {
//            eventDTO.setReporterId(currentUserId);
            eventDTO.setReporterId("1");
        }
        // TODO: 辅助决策、预案与事件等级判别说明、关联预案
        eventDTO.setEventLevel("2");
        eventDTO.setEmerPlanId("054908b4c5de4b71906875c7fe34a47c");
        eventDTO.setPlanLevelJudgment("根据《广西壮族自治区公路交通突发事件应急预案》，该预案适用于自治区范围内发生的Ⅱ级及以上公路交通突发事件。当国道、省道、高速公路发生交通中断，且抢修时间预计超过24小时时，应启动Ⅱ级应急响应。本次事件涉及泉南高速柳州段因山体塌方造成交通中断，伴随槽罐车粗苯泄漏和多车连环事故，抢险难度大、处置时间长，符合Ⅱ级响应启动条件。");
        eventDTO.setDecisionSupport("该事故发生在由广西高速公路管理有限公司柳州分公司负责的高速公路路段，已判定为重大公路交通突发事件。根据《广西壮族自治区公路交通突发事件应急预案》，符合Ⅱ级响应启动条件，建议启动Ⅱ级应急响应，由自治区交通运输厅统一指挥和调度。推荐派遣危化品处置专家、隧道工程专家及应急救援专家共同参与，确保高效处置。\n" +
                "根据事故现场山体塌方、危化品粗苯泄漏及多车人员被困等特点，需快速开展清障、救援和危化品转运处置工作。建议调配：挖掘机2台、装载机2台、运输车4辆；消防车1辆、救护车2辆、通讯保障车1辆；空载苯槽车1辆、单兵系统1套、无人机设备1套；具体配置可视现场情况动态调整。");
        // 插入主表
        int result = emergencyEventMapper.insertEmergencyEvent(eventDTO);

        if (result > 0) {
            // 根据事件类型插入扩展表
            if ("1".equals(createDTO.getEventType())) { // 道路交通事故
                EmergencyEventRoadTrafficDTO roadTrafficDTO = createDTO.toRoadTrafficDTO(eventId);
                roadTrafficDTO.setId(UUID.randomUUID().toString());
                roadTrafficDTO.setCreateTime(System.currentTimeMillis() / 1000);
                // 设置创建者ID
                roadTrafficDTO.setCreaterId(currentUserId);
                roadTrafficMapper.insertEmergencyEventRoadTraffic(roadTrafficDTO);
            } else if ("2".equals(createDTO.getEventType())) { // 水路交通事故
                EmergencyEventWaterwayTrafficDTO waterwayTrafficDTO = createDTO.toWaterwayTrafficDTO(eventId);
                waterwayTrafficDTO.setId(UUID.randomUUID().toString());
                waterwayTrafficDTO.setCreateTime(System.currentTimeMillis() / 1000);
                // 设置创建者ID
                waterwayTrafficDTO.setCreaterId(currentUserId);
                waterwayTrafficMapper.insertEmergencyEventWaterwayTraffic(waterwayTrafficDTO);
            }

            // 创建告警记录
            createEmergencyEventAlarm(createDTO, eventDTO);

            // 发送短信通知上报人
            sendSmsToSubmitter(createDTO, eventDTO);
        }

        return result;
    }

    /**
     * 修改应急事件
     *
     * @param createDTO 应急事件
     * @return 结果
     */
    @Override
    @Transactional
    public int updateEmergencyEvent(EmergencyEventCreateDTO createDTO) {
        // 转换为主表DTO并设置系统字段
        EmergencyEventDTO eventDTO = createDTO.toEmergencyEventDTO();
        eventDTO.setUpdateTime(System.currentTimeMillis() / 1000);
        // 设置更新者ID（从当前登录用户获取）
        String currentUserId = getCurrentUserId();
        eventDTO.setUpdaterId(currentUserId);

        // 更新主表
        int result = emergencyEventMapper.updateEmergencyEvent(eventDTO);

        if (result > 0) {
            String eventId = eventDTO.getEventId();

            // 根据事件类型更新扩展表
            if ("1".equals(createDTO.getEventType())) { // 道路交通事故
                // 先删除原有扩展数据
                waterwayTrafficMapper.deleteEmergencyEventWaterwayTrafficByEventId(eventId);

                // 插入或更新道路交通事故扩展数据
                EmergencyEventRoadTrafficDTO existingRoadTraffic = roadTrafficMapper.selectEmergencyEventRoadTrafficByEventId(eventId);
                EmergencyEventRoadTrafficDTO roadTrafficDTO = createDTO.toRoadTrafficDTO(eventId);
                roadTrafficDTO.setUpdateTime(System.currentTimeMillis() / 1000);

                if (existingRoadTraffic != null) {
                    roadTrafficDTO.setId(existingRoadTraffic.getId());
                    roadTrafficMapper.updateEmergencyEventRoadTraffic(roadTrafficDTO);
                } else {
                    roadTrafficDTO.setId(UUID.randomUUID().toString());
                    roadTrafficDTO.setCreateTime(System.currentTimeMillis() / 1000);
                    roadTrafficMapper.insertEmergencyEventRoadTraffic(roadTrafficDTO);
                }
            } else if ("2".equals(createDTO.getEventType())) { // 水路交通事故
                // 先删除原有扩展数据
                roadTrafficMapper.deleteEmergencyEventRoadTrafficByEventId(eventId);

                // 插入或更新水路交通事故扩展数据
                EmergencyEventWaterwayTrafficDTO existingWaterwayTraffic = waterwayTrafficMapper.selectEmergencyEventWaterwayTrafficByEventId(eventId);
                EmergencyEventWaterwayTrafficDTO waterwayTrafficDTO = createDTO.toWaterwayTrafficDTO(eventId);
                waterwayTrafficDTO.setUpdateTime(System.currentTimeMillis() / 1000);

                if (existingWaterwayTraffic != null) {
                    waterwayTrafficDTO.setId(existingWaterwayTraffic.getId());
                    waterwayTrafficMapper.updateEmergencyEventWaterwayTraffic(waterwayTrafficDTO);
                } else {
                    waterwayTrafficDTO.setId(UUID.randomUUID().toString());
                    waterwayTrafficDTO.setCreateTime(System.currentTimeMillis() / 1000);
                    waterwayTrafficMapper.insertEmergencyEventWaterwayTraffic(waterwayTrafficDTO);
                }
            }
        }

        return result;
    }

    /**
     * 批量删除应急事件
     *
     * @param eventIds 需要删除的应急事件主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteEmergencyEventByEventIds(String[] eventIds) {
        // 删除扩展表数据
        for (String eventId : eventIds) {
            roadTrafficMapper.deleteEmergencyEventRoadTrafficByEventId(eventId);
            waterwayTrafficMapper.deleteEmergencyEventWaterwayTrafficByEventId(eventId);
        }

        // 删除主表数据
        return emergencyEventMapper.deleteEmergencyEventByEventIds(eventIds);
    }

    /**
     * 删除应急事件信息
     *
     * @param eventId 应急事件主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteEmergencyEventByEventId(String eventId) {
        // 删除扩展表数据
        roadTrafficMapper.deleteEmergencyEventRoadTrafficByEventId(eventId);
        waterwayTrafficMapper.deleteEmergencyEventWaterwayTrafficByEventId(eventId);

        // 删除主表数据
        return emergencyEventMapper.deleteEmergencyEventByEventId(eventId);
    }

    /**
     * 更新事件状态
     *
     * @param eventId 事件ID
     * @param status  新状态
     * @return 结果
     */
    @Override
    public int updateEventStatus(String eventId, String status) {
        Long updateTime = System.currentTimeMillis() / 1000;
        // 获取当前登录用户ID
        String updaterId = getCurrentUserId();
        return emergencyEventMapper.updateEventStatus(eventId, status, updaterId, updateTime);
    }

    /**
     * 根据事件类型和时间范围查询事件列表
     *
     * @param eventType 事件类型
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 事件列表
     */
    @Override
    public List<EmergencyEventVO> selectEventsByTypeAndTime(String eventType, Long startTime, Long endTime) {
        List<EmergencyEventDTO> dtoList = emergencyEventMapper.selectEventsByTypeAndTime(eventType, startTime, endTime);
        return dtoList.stream().map(this::convertToVO).collect(Collectors.toList());
    }

    /**
     * 根据状态查询事件列表
     *
     * @param status 状态
     * @return 事件列表
     */
    @Override
    public List<EmergencyEventVO> selectEventsByStatus(String status) {
        List<EmergencyEventDTO> dtoList = emergencyEventMapper.selectEventsByStatus(status);
        return dtoList.stream().map(this::convertToVO).collect(Collectors.toList());
    }

    /**
     * 统计应急事件数量
     *
     * @param emergencyEvent 查询条件
     * @return 统计数量
     */
    @Override
    public int countEmergencyEvent(EmergencyEventDTO emergencyEvent) {
        return emergencyEventMapper.countEmergencyEvent(emergencyEvent);
    }

    @Override
    public void downloadNotice(HttpServletResponse response, String eventId) {
        //查询数据
        EmergencyEventDetailVO eventDto = this.selectEmergencyEventDetailByEventId(eventId);
        if (ObjectUtil.isNull(eventDto)) {
            throw new ServiceException("没有找到符合条件的应急事件");
        }
        //准备模板数据
        Map<String, String> templateData = prepareTemplateData(eventDto);

        // 添加调试日志
        log.info("模板数据准备完成，数据内容：");
        for (Map.Entry<String, String> entry : templateData.entrySet()) {
            log.info("占位符: {} = {}", entry.getKey(), entry.getValue());
        }

        try {
            //从resources加载新的模板并处理
            InputStream is = getClass().getClassLoader().getResourceAsStream("templates/emergency_leadership_notice_template.docx");
            if (is == null) {
                log.error("模板文件未找到: templates/emergency_leadership_notice_template.docx");
                throw new ServiceException("模板文件未找到");
            }

            //设置响应头
            String fileName = "关于成立应对" + eventDto.getEventTitle() + "应急处置工作领导小组的通知.docx";
            String encodedFileName = URLEncoder.encode(fileName, "UTF-8").replaceAll("\\+", "%20");
            response.setContentType("application/vnd.openxmlformats-officedocument.wordprocessingml.document");
            response.setHeader(HttpHeaders.CONTENT_DISPOSITION, "attachment;filename*=UTF-8''" + encodedFileName);

            //处理文档
            XWPFDocument document = new XWPFDocument(is);
            WordTemplateUtils.processDocument(document, templateData);

            //写入响应流
            document.write(response.getOutputStream());

            log.info("成功导出应急事件通知文档: {}", eventDto.getEventTitle());

        } catch (IOException e) {
            log.error("导出应急事件通知文档失败: " + e.getMessage(), e);
            throw new ServiceException("导出文档失败: " + e.getMessage());
        }
    }

    @Override
    @SneakyThrows
    public void exportEmergencyEventDecisionDocx(String eventId, HttpServletResponse response) {
        EmergencyEventDetailVO vo = this.selectEmergencyEventDetailByEventId(eventId);
        if (ObjectUtil.isNull(vo)) {
            throw new ServiceException("没有找到符合条件的应急事件");
        }
        // 遍历文档中的所有段落并替换文本
        try (
                InputStream resourceAsStream = getClass().getClassLoader().getResourceAsStream("templates/应急事件辅助决策建议模板.docx");
                BufferedInputStream bos = new BufferedInputStream(resourceAsStream);
                XWPFDocument document = new XWPFDocument(bos)
        ) {
            for (XWPFParagraph paragraph : document.getParagraphs()) {
                replaceInParagraph(paragraph, buildEventDecisionContent(vo));
            }
            // 响应修改后的文档
            String fileName = String.format("交通运输部门应对%s的辅助决策建议.docx", vo.getEventTitle());
            String trueName = URLEncoder.encode(fileName, StandardCharsets.UTF_8.name());
            response.setContentType("application/vnd.openxmlformats-officedocument.wordprocessingml.document");
            response.setHeader(HttpHeaders.CONTENT_DISPOSITION, "attachment;filename=" + trueName);
            document.write(response.getOutputStream());
        } catch (IOException ex) {
            log.error("交通运输部门应对{}的辅助决策建议文档导出异常: [{}]", vo.getEventTitle(), ex.getMessage(), ex);
            throw new ServiceException(String.format("交通运输部门应对%s的辅助决策建议文档导出异常", vo.getEventTitle()));
        }
    }

    private Map buildEventDecisionContent(EmergencyEventDetailVO vo) {
        StringBuffer contentTemplate = new StringBuffer("根据{0}突发事件对交通运输系统造成的影响，建议交通运输部门立即启动{1}级交通应急响应。经研判，此次事件【");
        Map<String, String> textMap = new HashMap<>();
        if (ObjectUtil.isNotNull(vo.getEstimatedRecoveryTime())) {
            DateTimeFormatter formatter = DateTimeFormatter
                    .ofPattern("yyyy年MM月dd日 HH时mm分ss秒")
                    .withZone(ZoneId.systemDefault()); // 设置系统默认时区
            String estimatedRecoveryTime = formatter.format(Instant.ofEpochSecond(vo.getEstimatedRecoveryTime()));
            contentTemplate.append(String.format("影响通行，预计于%s恢复通行，", estimatedRecoveryTime));
        }
        //{0}-标题，{1}-事件等级， {2}-伤亡情况，{3}-事件等级
        contentTemplate.append("目前{2}】，严重阻碍应急救援和物资运输通行，符合{3}级响应启动条件。响应启动后，迅速激活交通运输应急指挥体系，建立 24 小时应急值班制度，确保信息畅通、指令及时传达。");
        String level = LevelEnum.getRomeDescByCode(Integer.valueOf(vo.getEventLevel()));
        String content = MessageFormat.format(contentTemplate.toString(), vo.getEventTitle(), level, vo.getRoadCasualtySituation(), level);
        textMap.put("${eventTitle}", vo.getEventTitle());
        textMap.put("${respondContent}", content);
        return textMap;
    }


    private static void replaceInParagraph(XWPFParagraph paragraph, Map<String, String> replacements) {
        String text = paragraph.getText();
        if (text != null && !text.isEmpty()) {
            for (Map.Entry<String, String> entry : replacements.entrySet()) {
                if (text.contains(entry.getKey())) {
                    text = text.replace(entry.getKey(), entry.getValue());
                    // 清除原有内容
                    for (int i = paragraph.getRuns().size() - 1; i >= 0; i--) {
                        paragraph.removeRun(i);
                    }

                    CTP ctp = paragraph.getCTP();
                    CTPPr ppr = ctp.isSetPPr() ? ctp.getPPr() : ctp.addNewPPr();
                    CTSpacing spacing = ppr.isSetSpacing() ? ppr.getSpacing() : ppr.addNewSpacing();
                    // 2. 设置行距规则为固定值（关键！）
                    spacing.setLineRule(STLineSpacingRule.EXACT); // 必须设为 EXACT[1,6](@ref)
                    // 3. 设置28磅行距（1磅=20 twips → 28磅=560 twips）
                    spacing.setLine(BigInteger.valueOf(560)); // 单位：twips[1,7](@ref)
                    // 4. 可选：清除段前/段后间距
                    spacing.setAfter(BigInteger.valueOf(0));
                    spacing.setBefore(BigInteger.valueOf(0));
                    // 添加新文本（保留段落格式）
                    XWPFRun newRun = paragraph.createRun();
                    newRun.setText(text);
                    newRun.setFontFamily("仿宋");
                    newRun.setFontSize(16);
                    break; // 一次只替换一个占位符
                }
            }
        }
    }

    /**
     * 将DTO转换为VO
     *
     * @param dto DTO对象
     * @return VO对象
     */
    private EmergencyEventVO convertToVO(EmergencyEventDTO dto) {
        EmergencyEventVO vo = new EmergencyEventVO();
        BeanUtils.copyProperties(dto, vo);

        // TODO: 设置字典名称、用户名称等
        // 可以在这里调用字典服务和用户服务来获取名称

        return vo;
    }

    /**
     * 获取当前登录用户ID
     *
     * @return 当前用户ID
     */
    private String getCurrentUserId() {
        try {
            // 使用SecurityUtils获取当前登录用户ID
            Long userId = SecurityUtils.getUserId();
            return userId != null ? userId.toString() : null;
        } catch (Exception e) {
            // 如果获取失败，返回null或默认值
            return null;
        }
    }

    /**
     * 创建应急事件告警
     *
     * @param createDTO 应急事件创建DTO（包含完整数据）
     * @param eventDTO  应急事件DTO（主表数据）
     */
    private void createEmergencyEventAlarm(EmergencyEventCreateDTO createDTO, EmergencyEventDTO eventDTO) {
        try {
            AlarmInfoDTO alarmInfo = new AlarmInfoDTO();

            // 设置告警标题：xx行政辖区发生xx事件
            String alarmTitle = buildAlarmTitle(createDTO);
            alarmInfo.setAlarmTitle(alarmTitle);

            alarmInfo.setAlarmType("2"); // 应急类型
            alarmInfo.setAlarmSubtype("2"); // 新事件子类型
            alarmInfo.setAlarmLevel(mapEventLevelToAlarmLevel(eventDTO.getEventLevel()));

            // 设置详细的告警内容
            String alarmContent = buildAlarmContent(createDTO);
            alarmInfo.setAlarmContent(alarmContent);

            alarmInfo.setSourceId(eventDTO.getEventId());
            alarmInfo.setSourceType("event");

            // 设置行政辖区信息
            alarmInfo.setAdministrativeAreaId(eventDTO.getAdministrativeAreaId());
            alarmInfo.setAdministrativeArea(eventDTO.getAdministrativeArea());

            // 通过填报人ID查询用户信息获取部门信息
            String reporterId = eventDTO.getReporterId();
            if (reporterId != null) {
                try {
                    Long userId = Long.parseLong(reporterId);
                    SysUser reporter = userService.selectUserById(userId);
                    if (reporter != null && reporter.getDept() != null) {
                        alarmInfo.setOrgId(reporter.getDeptId().toString());
                        alarmInfo.setOrgName(reporter.getDept().getDeptName());
                    } else {
                        // 如果查询不到部门信息，使用默认值
                        alarmInfo.setOrgId(reporterId);
                        alarmInfo.setOrgName("未知部门");
                    }
                } catch (NumberFormatException e) {
                    // 如果填报人ID不是数字，使用默认值
                    alarmInfo.setOrgId(reporterId);
                    alarmInfo.setOrgName("未知部门");
                }
            } else {
                // 如果没有填报人ID，使用当前用户的部门信息
                Long currentUserId = SecurityUtils.getUserId();
                Long currentDeptId = SecurityUtils.getDeptId();
                alarmInfo.setOrgId(currentDeptId != null ? currentDeptId.toString() : "");
                alarmInfo.setOrgName("当前用户部门");
            }


            // 调用告警服务创建告警
            alarmService.insertAlarmInfo(alarmInfo);
        } catch (Exception e) {
            // 告警创建失败不影响主业务流程，只记录日志
            log.error("创建应急事件告警失败: " + e.getMessage(), e);
        }
    }

    /**
     * 构建告警标题
     *
     * @param createDTO 应急事件创建DTO
     * @return 告警标题
     */
    private String buildAlarmTitle(EmergencyEventCreateDTO createDTO) {
        String administrativeArea = createDTO.getAdministrativeArea();
        String eventTypeName = getEventTypeName(createDTO.getEventType());

        // 格式：xx行政辖区发生xx事件
        return String.format("%s发生%s",
                administrativeArea != null ? administrativeArea : "未知区域",
                eventTypeName != null ? eventTypeName : "突发事件");
    }

    /**
     * 构建告警内容
     *
     * @param createDTO 应急事件创建DTO
     * @return 告警内容
     */
    private String buildAlarmContent(EmergencyEventCreateDTO createDTO) {
        StringBuilder content = new StringBuilder();

        // 发生时间
        if (createDTO.getOccurTime() != null) {
            content.append(formatTimestampToChinese(createDTO.getOccurTime()));
        }

        // 事故地址
        if (createDTO.getDetailedAddress() != null) {
            content.append("，在").append(createDTO.getDetailedAddress());
        }

        // 事件类型和事故类型
        String eventTypeName = getEventTypeName(createDTO.getEventType());
        if (eventTypeName != null) {
            content.append("发生一起").append(eventTypeName);
        }

        // 事件描述（原因）
        if (createDTO.getEventDescription() != null) {
            content.append("，").append(createDTO.getEventDescription());
        }

        // 根据事件类型添加扩展信息
        if ("1".equals(createDTO.getEventType())) { // 道路交通事故
            appendRoadTrafficInfoText(content, createDTO);
        } else if ("2".equals(createDTO.getEventType())) { // 水路交通事故
            appendWaterwayTrafficInfoText(content, createDTO);
        }

        content.append("。");
        return content.toString();
    }

    /**
     * 将事件级别映射为告警级别
     *
     * @param eventLevel 事件级别
     * @return 告警级别
     */
    private String mapEventLevelToAlarmLevel(String eventLevel) {
        if (eventLevel == null) {
            return "1"; // 默认一般级别
        }

        switch (eventLevel) {
            case "1": // 事件Ⅰ级(特别重大)
                return "4"; // 告警严重
            case "2": // 事件Ⅱ级(重大)
                return "3"; // 告警紧急
            case "3": // 事件Ⅲ级(较大)
                return "2"; // 告警重要
            case "4": // 事件Ⅳ级(一般)
                return "1"; // 告警一般
            default:
                return "1"; // 默认一般级别
        }
    }

    /**
     * 添加道路交通事故扩展信息（文本格式）
     *
     * @param content   内容构建器
     * @param createDTO 应急事件创建DTO
     */
    private void appendRoadTrafficInfoText(StringBuilder content, EmergencyEventCreateDTO createDTO) {
        // 是否影响通行
        if (createDTO.getTrafficAffected() != null) {
            if ("Y".equals(createDTO.getTrafficAffected())) {
                content.append("。该事故影响通行");

                // 预计恢复时间
                if (createDTO.getEstimatedRecoveryTime() != null) {
                    content.append("，预计于").append(formatTimestampToChinese(createDTO.getEstimatedRecoveryTime())).append("恢复通行");
                }
            } else {
                content.append("。该事故不影响通行");
            }
        }

        // 人员伤亡情况
        if (createDTO.getRoadCasualtySituation() != null) {
            content.append("，目前").append(createDTO.getRoadCasualtySituation());
        }
    }

    /**
     * 添加水路交通事故扩展信息（文本格式）
     *
     * @param content   内容构建器
     * @param createDTO 应急事件创建DTO
     */
    private void appendWaterwayTrafficInfoText(StringBuilder content, EmergencyEventCreateDTO createDTO) {
        // 船舶信息
        if (createDTO.getShipName() != null) {
            content.append("，涉事船舶为").append(createDTO.getShipName());

            // 船舶吨位
            if (createDTO.getShipTonnage() != null) {
                content.append("（").append(createDTO.getShipTonnage()).append("吨）");
            }
        }

        // 货物信息
        if (createDTO.getCargoInfo() != null) {
            content.append("，载有").append(createDTO.getCargoInfo());
        }

        // 人员伤亡情况
        if (createDTO.getWaterwayCasualtySituation() != null) {
            content.append("，目前").append(createDTO.getWaterwayCasualtySituation());
        }

        // 环境影响
        if (createDTO.getEnvironmentalImpact() != null) {
            content.append("，").append(createDTO.getEnvironmentalImpact());
        }
    }

    /**
     * 格式化时间戳
     *
     * @param timestamp 时间戳（秒）
     * @return 格式化后的时间字符串
     */
    private String formatTimestamp(Long timestamp) {
        if (timestamp == null) {
            return "";
        }
        try {
            java.util.Date date = new java.util.Date(timestamp * 1000);
            java.text.SimpleDateFormat sdf = new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            return sdf.format(date);
        } catch (Exception e) {
            return timestamp.toString();
        }
    }

    /**
     * 格式化时间戳为中文年月日时分秒格式
     *
     * @param timestamp 时间戳（秒）
     * @return 格式化后的中文时间字符串（如：2025年06月01日17时25分30秒）
     */
    private String formatTimestampToChinese(Long timestamp) {
        if (timestamp == null) {
            return "";
        }
        try {
            java.util.Date date = new java.util.Date(timestamp * 1000);
            java.text.SimpleDateFormat sdf = new java.text.SimpleDateFormat("yyyy年MM月dd日HH时mm分ss秒");
            return sdf.format(date);
        } catch (Exception e) {
            return formatTimestamp(timestamp);
        }
    }

    /**
     * 获取事件类型名称
     * TODO: 这里应该调用字典服务获取真实的字典值，暂时使用硬编码
     *
     * @param eventType 事件类型值
     * @return 事件类型名称
     */
    private String getEventTypeName(String eventType) {
        if (eventType == null) {
            return null;
        }
        switch (eventType) {
            case "1":
                return "道路交通事故";
            case "2":
                return "水路交通事故";
            default:
                return "突发事件";
        }
    }

    /**
     * 获取事故类型名称
     * TODO: 这里应该调用字典服务获取真实的字典值，暂时使用硬编码
     *
     * @param accidentType 事故类型值
     * @return 事故类型名称
     */
    private String getAccidentTypeName(String accidentType) {
        if (accidentType == null) {
            return null;
        }
        // 这里需要根据实际的字典数据进行映射
        // 暂时返回原值，后续可以调用字典服务
        return accidentType;
    }

    /**
     * 获取船舶类型名称
     * TODO: 这里应该调用字典服务获取真实的字典值，暂时使用硬编码
     *
     * @param shipType 船舶类型值
     * @return 船舶类型名称
     */
    private String getShipTypeName(String shipType) {
        if (shipType == null) {
            return null;
        }
        // 这里需要根据实际的字典数据进行映射
        // 暂时返回原值，后续可以调用字典服务
        return shipType;
    }

    public static void main(String[] args) {
        String token = getYkToken();
        System.out.println(token);
        NewEmerEventSmsDTO dto = new NewEmerEventSmsDTO();
        dto.setDate("2025年06月03日11时22分");
        dto.setLocation("G75兰海高速上行K2012+300");
        dto.setContent("发生一辆小车抛锚");
        smsSend(token, dto);
    }

    private static void smsSend(String token, NewEmerEventSmsDTO dto) {
        // 测试短信发送
        smsSend(token, "18176275992", "测试短信内容");
    }

    /**
     * 发送短信通知上报人
     *
     * @param createDTO 应急事件创建DTO
     * @param eventDTO  应急事件DTO
     */
    private void sendSmsToSubmitter(EmergencyEventCreateDTO createDTO, EmergencyEventDTO eventDTO) {
        try {
            // 获取上报人手机号
            String submitterMobile = getSubmitterMobile(eventDTO.getSubmitterId());
            if (submitterMobile == null || submitterMobile.isEmpty()) {
                log.warn("上报人手机号为空，无法发送短信通知，上报人ID: {}", eventDTO.getSubmitterId());
                return;
            }
            // 构建短信内容
            String smsContent = buildSmsContent(createDTO);

            // 获取token并发送短信
            String token = getYkToken();
            if (token != null) {
                smsSend(token, submitterMobile, smsContent);
                log.info("短信发送成功，手机号: {}, 内容: {}", submitterMobile, smsContent);
            } else {
                log.error("获取短信token失败，无法发送短信");
            }
        } catch (Exception e) {
            // 短信发送失败不影响主业务流程，只记录日志
            log.error("发送短信通知失败: " + e.getMessage(), e);
        }
    }

    /**
     * 获取上报人手机号
     *
     * @param submitterId 上报人ID
     * @return 手机号
     */
    private String getSubmitterMobile(String submitterId) {
        if (submitterId == null || submitterId.isEmpty()) {
            return null;
        }

        try {
            Long userId = Long.parseLong(submitterId);
            SysUser submitter = userService.selectUserById(userId);
            return submitter != null ? submitter.getPhonenumber() : null;
        } catch (NumberFormatException e) {
            log.error("上报人ID格式错误: {}", submitterId);
            return null;
        }
    }

    /**
     * 构建短信内容（JSON格式）
     *
     * @param createDTO 应急事件创建DTO
     * @return 短信内容（JSON格式）
     */
    private String buildSmsContent(EmergencyEventCreateDTO createDTO) {
        // 构建date字段（格式：2025年06月03日11时22分30秒）
        String dateStr = "";
        if (createDTO.getOccurTime() != null) {
            dateStr = formatTimestampToChinese(createDTO.getOccurTime());
        }

        // 构建location字段
        String locationStr = createDTO.getDetailedAddress() != null ? createDTO.getDetailedAddress() : "";

        // 构建content字段（参考告警内容格式）
        StringBuilder contentBuilder = new StringBuilder();

        // 事件类型
        String eventTypeName = getEventTypeName(createDTO.getEventType());
        if (eventTypeName != null) {
            contentBuilder.append("发生一起").append(eventTypeName);
        }

        // 事件描述
        if (createDTO.getEventDescription() != null && !createDTO.getEventDescription().isEmpty()) {
            contentBuilder.append("，").append(createDTO.getEventDescription());
        }

        // 根据事件类型添加扩展信息
        if ("1".equals(createDTO.getEventType())) { // 道路交通事故
            appendRoadTrafficSmsInfo(contentBuilder, createDTO);
        } else if ("2".equals(createDTO.getEventType())) { // 水路交通事故
            appendWaterwayTrafficSmsInfo(contentBuilder, createDTO);
        }

        // 构建JSON格式的内容
        return String.format("{\"date\":\"%s\",\"location\":\"%s\",\"content\":\"%s\"}",
                dateStr, locationStr, contentBuilder.toString());
    }

    /**
     * 添加道路交通事故扩展信息（短信格式）
     *
     * @param content   内容构建器
     * @param createDTO 应急事件创建DTO
     */
    private void appendRoadTrafficSmsInfo(StringBuilder content, EmergencyEventCreateDTO createDTO) {
        // 是否影响通行
        if (createDTO.getTrafficAffected() != null) {
            if ("Y".equals(createDTO.getTrafficAffected())) {
                content.append("。该事故影响通行");

                // 预计恢复时间
                if (createDTO.getEstimatedRecoveryTime() != null) {
                    String recoveryTimeStr = formatTimestampToChinese(createDTO.getEstimatedRecoveryTime());
                    content.append("，预计于").append(recoveryTimeStr).append("恢复通行");
                }
            } else {
                content.append("。该事故不影响通行");
            }
        }

        // 人员伤亡情况
        if (createDTO.getRoadCasualtySituation() != null && !createDTO.getRoadCasualtySituation().isEmpty()) {
            content.append("，目前").append(createDTO.getRoadCasualtySituation());
        }
    }

    /**
     * 添加水路交通事故扩展信息（短信格式）
     *
     * @param content   内容构建器
     * @param createDTO 应急事件创建DTO
     */
    private void appendWaterwayTrafficSmsInfo(StringBuilder content, EmergencyEventCreateDTO createDTO) {
        // 船舶信息
        if (createDTO.getShipName() != null && !createDTO.getShipName().isEmpty()) {
            content.append("，涉事船舶为").append(createDTO.getShipName());

            // 船舶吨位
            if (createDTO.getShipTonnage() != null) {
                content.append("（").append(createDTO.getShipTonnage()).append("吨）");
            }
        }

        // 货物信息
        if (createDTO.getCargoInfo() != null && !createDTO.getCargoInfo().isEmpty()) {
            content.append("，载有").append(createDTO.getCargoInfo());
        }

        // 人员伤亡情况
        if (createDTO.getWaterwayCasualtySituation() != null && !createDTO.getWaterwayCasualtySituation().isEmpty()) {
            content.append("，目前").append(createDTO.getWaterwayCasualtySituation());
        }

        // 环境影响
        if (createDTO.getEnvironmentalImpact() != null && !createDTO.getEnvironmentalImpact().isEmpty()) {
            content.append("，").append(createDTO.getEnvironmentalImpact());
        }
    }

    private static void smsSend(String token, String mobile, String content) {
        String url = "https://yktest.itsgx.cn:18763/sykpt/its-api/smsSend";
        Map<String, String> head = new HashMap<>();
        head.put("AuthorizationType", "other");
        head.put("Authorization", token);
        Map<String, String> body = new HashMap<>();
        body.put("mobile", mobile);
        body.put("content", content);
        body.put("signName", "智慧高速云控平台");
        body.put("templateKey","NEW_EMER_EVENT");
        String response = HttpClientUtils.postWithBody(10000, url, head, body);
        log.info("短信发送响应: {}", response);
    }

    private static String getYkToken() {
        String url = "https://yktest.itsgx.cn:18763/sykpt/its-api/login/otherGetToken";
        Map<String, String> body = new HashMap<>();
        body.put("appId", "fb9686ae-8706-46ce-926d-23ddfbc010e9");
        body.put("appSecret", "WTKkpkH*wAyFCKgbbfNz$sMnbuqd#Wj#");
        String response = HttpClientUtils.postWithBody(10000, url, null, body);
        YkTokenVO vo = new Gson().fromJson(response, YkTokenVO.class);
        if (vo.getCode() == 1) {
            return vo.getToken();
        }
        return null;
    }

    /**
     * 导出通知模板替换数据
     */
    private Map<String, String> prepareTemplateData(EmergencyEventDetailVO vo) {
        Map<String, String> data = new HashMap<>();

        // 基础数据
        data.put("${eventTitle}", vo.getEventTitle() != null ? vo.getEventTitle() : "突发事件");
        data.put("${noticeContent}", buildFullNoticeContent(vo));

        //获取当前的用户
        LoginUser loginUser = SecurityUtils.getLoginUser();
        if (ObjectUtil.isNotNull(loginUser)) {
            SysUser currentUser = loginUser.getUser();
            if (StringUtils.isNotNull(currentUser) && StringUtils.isNotNull(currentUser.getDept())) {
                data.put("${unitName}", currentUser.getDept().getDeptName());
            }
        }
        String time = DateUtil.format(DateUtil.date(), "yyyy年MM月dd日");
        data.put("${time}", time);

        // 获取领导小组成员信息
        Map<String, String> leadershipData = buildDetailedLeadershipData(vo);
        data.putAll(leadershipData);

        // 获取工作组信息
        Map<String, String> workGroupData = buildWorkGroupData(vo);
        data.putAll(workGroupData);

        // 其他占位符
        data.put("${localCommandStructure}", "市、县级应急指挥机构按照相关预案执行");

        return data;
    }

    /**
     * 构建通知突发事件描述
     *
     * @param vo
     * @return 突发事件描述
     */
    private String buildNoticeContent(EmergencyEventDetailVO vo) {
        StringBuilder content = new StringBuilder();
        //事故地址
        if (vo.getDetailedAddress() != null) {
            content.append("在").append(vo.getDetailedAddress());
        }
        //事件类型和事故类型
        String eventTypeName = getEventTypeName(vo.getEventType());
        if (eventTypeName != null) {
            content.append("发生一起").append(eventTypeName);
        }
        //事件描述（原因）
        if (vo.getEventDescription() != null) {
            content.append("，").append(vo.getEventDescription());
        }
        //根据事件类型添加扩展信息
        if ("1".equals(vo.getEventType())) { //道路交通事故
            //是否影响通行
            if (vo.getTrafficAffected() != null) {
                if ("Y".equals(vo.getTrafficAffected())) {
                    content.append("。该事故影响通行");
                    // 预计恢复时间
                    if (vo.getEstimatedRecoveryTime() != null) {
                        content.append("，预计于").append(formatTimestampToChinese(vo.getEstimatedRecoveryTime())).append("恢复通行");
                    }
                } else {
                    content.append("。该事故不影响通行");
                }
            }
            //人员伤亡情况
            if (vo.getRoadCasualtySituation() != null) {
                content.append("，目前").append(vo.getRoadCasualtySituation());
            }
        } else if ("2".equals(vo.getEventType())) { //水路交通事故
            //船舶信息
            if (vo.getShipName() != null) {
                content.append("，涉事船舶为").append(vo.getShipName());
                //船舶吨位
                if (vo.getShipTonnage() != null) {
                    content.append("（").append(vo.getShipTonnage()).append("吨）");
                }
            }
            //货物信息
            if (vo.getCargoInfo() != null) {
                content.append("，载有").append(vo.getCargoInfo());
            }
            //人员伤亡情况
            if (vo.getWaterwayCasualtySituation() != null) {
                content.append("，目前").append(vo.getWaterwayCasualtySituation());
            }
            //环境影响
            if (vo.getEnvironmentalImpact() != null) {
                content.append("，").append(vo.getEnvironmentalImpact());
            }
        }
        return content.toString();
    }

    /**
     * 构建完整的通知正文内容
     * 按照模板格式构建：[具体时间] 发生 [简要描述某某突发事件，如 "某某地区因连续强降雨引发严重洪涝灾害"]，
     * 对人民群众生命财产安全和社会稳定造成较大影响。为有效应对该突发事件，高效开展应急处置工作，
     * 经 [上级部门或相关会议] 研究决定，成立应对某某突发事件应急处置工作领导小组。现将有关事项通知如下：
     *
     * @param vo 应急事件详情
     * @return 完整的通知正文内容
     */
    private String buildFullNoticeContent(EmergencyEventDetailVO vo) {
        StringBuilder content = new StringBuilder();

        try {
            // 1. 具体时间
            if (vo.getOccurTime() != null) {
                content.append(formatTimestampToChinese(vo.getOccurTime()));
            } else {
                content.append("[具体时间]");
            }

            // 2. 发生
            content.append("发生");

            // 3. 简要描述突发事件
            String eventDescription = buildEventDescription(vo);
            content.append(eventDescription);

            // 4. 影响描述
            content.append("，对人民群众生命财产安全和社会稳定造成较大影响。");

            // 5. 应对措施说明
            content.append("为有效应对该突发事件，高效开展应急处置工作，经");

            // 6. 决定机构（从当前用户部门获取）
            LoginUser loginUser = SecurityUtils.getLoginUser();
            if (ObjectUtil.isNotNull(loginUser) && loginUser.getUser() != null && loginUser.getUser().getDept() != null) {
                content.append(loginUser.getUser().getDept().getDeptName()).append("相关会议");
            } else {
                content.append("上级部门或相关会议");
            }

            // 7. 成立领导小组
            content.append("研究决定，成立应对");
            if (vo.getEventTitle() != null && !vo.getEventTitle().trim().isEmpty()) {
                content.append(vo.getEventTitle());
            } else {
                content.append("突发事件");
            }
            content.append("应急处置工作领导小组。现将有关事项通知如下：");

        } catch (Exception e) {
            log.error("构建完整通知正文内容失败", e);
            return "通知正文内容构建失败";
        }

        return content.toString();
    }

    /**
     * 构建事件描述部分
     * 格式：[简要描述某某突发事件，如 "某某地区因连续强降雨引发严重洪涝灾害"]
     *
     * @param vo 应急事件详情
     * @return 事件描述内容
     */
    private String buildEventDescription(EmergencyEventDetailVO vo) {
        StringBuilder description = new StringBuilder();

        // 地点信息
        if (vo.getDetailedAddress() != null && !vo.getDetailedAddress().trim().isEmpty()) {
            description.append(vo.getDetailedAddress());
        } else {
            description.append("某某地区");
        }

        // 事件类型和原因
        String eventTypeName = getEventTypeName(vo.getEventType());
        if (vo.getEventDescription() != null && !vo.getEventDescription().trim().isEmpty()) {
            description.append("因").append(vo.getEventDescription());
            if (eventTypeName != null) {
                description.append("引发").append(eventTypeName);
            }
        } else if (eventTypeName != null) {
            description.append("发生").append(eventTypeName);
        } else {
            description.append("发生突发事件");
        }

        return description.toString();
    }

    /**
     * 构建领导小组组成人员内容
     *
     * @param vo 应急事件详情
     * @return 领导小组组成人员内容
     */
    private String buildLeadershipGroupContent(EmergencyEventDetailVO vo) {
        try {
            // 1. 获取关联预案ID（写死或从事件中获取）
            String prePlanId = "d34e93a027fd44d9bca6be7f2fcf8e28";
            log.info("开始获取预案数据，预案ID: {}", prePlanId);

            // 2. 调用预案服务获取组织架构
            EmPrePlanVO prePlanVO = emPrePlanService.selectEmPrePlanById(prePlanId);
            if (prePlanVO == null) {
                log.warn("未找到预案数据，预案ID: {}", prePlanId);
                return "暂无领导小组信息（未找到预案）";
            }

            if (prePlanVO.getEmPrePlanDeptDTOList() == null) {
                log.warn("预案组织架构数据为空，预案ID: {}", prePlanId);
                return "暂无领导小组信息（组织架构为空）";
            }

            log.info("成功获取预案数据，组织架构数量: {}", prePlanVO.getEmPrePlanDeptDTOList().size());

            // 3. 格式化组织架构数据为Word文档格式
            String result = formatLeadershipGroup(prePlanVO.getEmPrePlanDeptDTOList());
            log.info("领导小组内容格式化完成，长度: {}", result.length());
            return result;
        } catch (Exception e) {
            log.error("构建领导小组组成人员内容失败: " + e.getMessage(), e);
            return "领导小组信息获取失败: " + e.getMessage();
        }
    }

    /**
     * 构建应急组织架构内容
     *
     * @param vo 应急事件详情
     * @return 应急组织架构内容
     */
    private String buildOrganizationStructureContent(EmergencyEventDetailVO vo) {
        try {
            // 1. 获取关联预案ID（写死）
            String prePlanId = "d34e93a027fd44d9bca6be7f2fcf8e28";
            log.info("开始获取组织架构数据，预案ID: {}", prePlanId);

            // 2. 调用预案服务获取组织架构
            EmPrePlanVO prePlanVO = emPrePlanService.selectEmPrePlanById(prePlanId);
            if (prePlanVO == null) {
                log.warn("未找到预案数据，预案ID: {}", prePlanId);
                return "暂无组织架构信息（未找到预案）";
            }

            if (prePlanVO.getEmPrePlanDeptDTOList() == null) {
                log.warn("预案组织架构数据为空，预案ID: {}", prePlanId);
                return "暂无组织架构信息（组织架构为空）";
            }

            log.info("成功获取组织架构数据，组织架构数量: {}", prePlanVO.getEmPrePlanDeptDTOList().size());

            // 3. 格式化组织架构数据为Word文档格式
            String result = formatOrganizationStructure(prePlanVO.getEmPrePlanDeptDTOList());
            log.info("组织架构内容格式化完成，长度: {}", result.length());
            return result;
        } catch (Exception e) {
            log.error("构建应急组织架构内容失败: " + e.getMessage(), e);
            return "组织架构信息获取失败: " + e.getMessage();
        }
    }

    /**
     * 格式化领导小组组成人员
     * 从组织架构中提取核心领导小组成员，格式化为通知文件需要的格式
     *
     * @param deptList 应急预案组织体系列表
     * @return 格式化后的领导小组组成人员内容
     */
    private String formatLeadershipGroup(java.util.List<EmPrePlanDeptDTO> deptList) {
        StringBuilder content = new StringBuilder();

        try {
            // 查找"领导小组"机构
            EmPrePlanDeptDTO leadershipGroup = findDeptByName(deptList, "领导小组");

            if (leadershipGroup == null) {
                log.warn("未找到'领导小组'机构");
                return "未找到领导小组信息";
            }

            // 1. 提取组长信息（从leader字段）
            if (leadershipGroup.getLeader() != null && !leadershipGroup.getLeader().trim().isEmpty()) {
                PersonInfo leader = parsePersonInfo(leadershipGroup.getLeader());
                if (!leader.getName().isEmpty()) {
                    content.append("组长：").append(formatPersonInfoForLeadership(leader)).append("\n\n");
                }
            }

            // 2. 提取副组长信息（从leaderAss字段）
            if (leadershipGroup.getLeaderAss() != null && !leadershipGroup.getLeaderAss().trim().isEmpty()) {
                java.util.List<PersonInfo> assistants = parseMultiplePersonInfo(leadershipGroup.getLeaderAss());
                if (!assistants.isEmpty()) {
                    content.append("副组长：");
                    for (int i = 0; i < assistants.size(); i++) {
                        if (i > 0) {
                            content.append("        "); // 对齐缩进
                        }
                        content.append(formatPersonInfoForLeadership(assistants.get(i))).append("\n");
                    }
                    content.append("\n");
                }
            }

            // 3. 提取成员信息（从member字段）
            if (leadershipGroup.getMember() != null && !leadershipGroup.getMember().trim().isEmpty()) {
                java.util.List<PersonInfo> members = parseMultiplePersonInfo(leadershipGroup.getMember());
                if (!members.isEmpty()) {
                    content.append("成员：");
                    for (int i = 0; i < members.size(); i++) {
                        if (i > 0) {
                            content.append("      "); // 对齐缩进
                        }
                        content.append(formatPersonInfoForLeadership(members.get(i))).append("\n");
                    }
                }
            }

        } catch (Exception e) {
            log.error("格式化领导小组组成人员失败", e);
            return "领导小组信息格式化失败";
        }

        return content.toString();
    }

    /**
     * 根据机构名称查找机构
     * 支持递归查找子机构
     *
     * @param deptList 机构列表
     * @param deptName 机构名称
     * @return 找到的机构，未找到返回null
     */
    private EmPrePlanDeptDTO findDeptByName(java.util.List<EmPrePlanDeptDTO> deptList, String deptName) {
        if (deptList == null || deptName == null) {
            return null;
        }

        for (EmPrePlanDeptDTO dept : deptList) {
            // 检查当前机构名称
            if (deptName.equals(dept.getDeptName())) {
                return dept;
            }

            // 递归查找子机构
            if (dept.getChildren() != null && !dept.getChildren().isEmpty()) {
                EmPrePlanDeptDTO found = findDeptByName(dept.getChildren(), deptName);
                if (found != null) {
                    return found;
                }
            }
        }

        return null;
    }

    /**
     * 格式化人员信息用于领导小组显示
     * 输出格式: "李明华(自治区交通运输厅厅长) 联系方式：13907711001"
     *
     * @param person 人员信息对象
     * @return 格式化后的人员信息字符串
     */
    private String formatPersonInfoForLeadership(PersonInfo person) {
        if (person == null || person.getName().isEmpty()) {
            return "";
        }

        StringBuilder formatted = new StringBuilder();
        formatted.append(person.getName());

        // 添加职务信息
        if (!person.getPosition().isEmpty()) {
            formatted.append("(").append(person.getPosition()).append(")");
        }

        // 添加联系方式
        if (!person.getPhone().isEmpty()) {
            formatted.append(" 联系方式：").append(person.getPhone());
        }

        return formatted.toString();
    }

    /**
     * 格式化完整组织架构
     * 按照模板格式，分别输出组成成员和主要职责
     *
     * @param deptList 应急预案组织体系列表
     * @return 格式化后的组织架构内容
     */
    private String formatOrganizationStructure(java.util.List<EmPrePlanDeptDTO> deptList) {
        StringBuilder content = new StringBuilder();

        try {
            // 遍历所有机构
            for (EmPrePlanDeptDTO dept : deptList) {
                content.append(formatSingleDept(dept));

                // 递归处理子机构
                if (dept.getChildren() != null && !dept.getChildren().isEmpty()) {
                    content.append(formatOrganizationStructure(dept.getChildren()));
                }
            }
        } catch (Exception e) {
            log.error("格式化组织架构失败", e);
            return "组织架构信息格式化失败";
        }

        return content.toString();
    }

    /**
     * 格式化单个机构信息
     * 按照模板格式输出：机构名称 + （1）组成成员 + (2)主要职责
     *
     * @param dept 机构信息
     * @return 格式化后的单个机构内容
     */
    private String formatSingleDept(EmPrePlanDeptDTO dept) {
        StringBuilder content = new StringBuilder();

        // 1. 输出机构名称
        content.append(dept.getDeptName()).append("\n");

        // 2. 输出组成成员部分
        content.append("（1）组成成员\n");
        content.append(formatDeptMembers(dept));

        // 3. 输出主要职责部分
        content.append("(2)主要职责\n");
        content.append(formatDeptJob(dept.getDeptJob()));

        return content.toString();
    }

    /**
     * 格式化部门成员信息
     * 按照模板格式输出副组长和成员信息
     *
     * @param dept 机构信息
     * @return 格式化后的成员信息
     */
    private String formatDeptMembers(EmPrePlanDeptDTO dept) {
        StringBuilder members = new StringBuilder();

        // 处理副组长
        if (dept.getLeaderAss() != null && !dept.getLeaderAss().trim().isEmpty()) {
            members.append("副组长：");
            java.util.List<PersonInfo> assistants = parseMultiplePersonInfo(dept.getLeaderAss());
            for (int i = 0; i < assistants.size(); i++) {
                PersonInfo person = assistants.get(i);
                if (i == 0) {
                    members.append(formatPersonInfoCompact(person));
                } else {
                    members.append(" ").append(formatPersonInfoCompact(person));
                }
            }
            members.append("\n");
        }

        // 处理成员
        if (dept.getMember() != null && !dept.getMember().trim().isEmpty()) {
            members.append("成员：");
            java.util.List<PersonInfo> memberList = parseMultiplePersonInfo(dept.getMember());
            for (int i = 0; i < memberList.size(); i++) {
                PersonInfo person = memberList.get(i);
                if (i == 0) {
                    members.append(formatPersonInfoCompact(person));
                } else {
                    members.append(" ").append(formatPersonInfoCompact(person));
                }
            }
            members.append("......（根据实际情况列出其他成员及信息）\n");
        }

        // 如果没有副组长和成员信息，显示提示
        if (members.length() == 0) {
            members.append("成员信息待完善\n");
        }

        return members.toString();
    }

    /**
     * 格式化部门职责
     * 处理职责信息，如果为空或无效则显示默认提示
     *
     * @param deptJob 部门职责字符串
     * @return 格式化后的职责内容
     */
    private String formatDeptJob(String deptJob) {
        if (deptJob == null || deptJob.trim().isEmpty() || "string".equals(deptJob.trim())) {
            return "职责待完善\n";
        }

        // 如果职责内容已经有编号格式，直接返回
        if (deptJob.contains("1.") || deptJob.contains("（1）") || deptJob.contains("(1)")) {
            return deptJob + "\n";
        }

        // 否则添加基本格式
        return "1. " + deptJob + "\n";
    }

    /**
     * 解析人员信息字符串
     * 输入格式: "李明华(自治区交通运输厅厅长) 13907711001"
     * 输出: PersonInfo{name="李明华", position="自治区交通运输厅厅长", phone="13907711001"}
     *
     * @param personStr 人员信息字符串
     * @return PersonInfo对象，解析失败时返回空字段的PersonInfo
     */
    private PersonInfo parsePersonInfo(String personStr) {
        if (personStr == null || personStr.trim().isEmpty()) {
            return new PersonInfo("", "", "");
        }

        try {
            // 正则表达式匹配: 姓名(职务) 电话
            // 支持格式: "李明华(自治区交通运输厅厅长) 13907711001"
            Pattern pattern = Pattern.compile("^(.+?)\\((.+?)\\)\\s+(.+)$");
            Matcher matcher = pattern.matcher(personStr.trim());

            if (matcher.matches()) {
                String name = matcher.group(1).trim();
                String position = matcher.group(2).trim();
                String phone = matcher.group(3).trim();
                return new PersonInfo(name, position, phone);
            } else {
                // 如果正则匹配失败，尝试简单分割
                log.warn("人员信息格式不匹配，尝试简单解析: " + personStr);
                return parsePersonInfoSimple(personStr);
            }
        } catch (Exception e) {
            log.error("解析人员信息失败: " + personStr, e);
            return new PersonInfo(personStr, "", "");
        }
    }

    /**
     * 简单解析人员信息（备用方法）
     */
    private PersonInfo parsePersonInfoSimple(String personStr) {
        // 简单处理，将整个字符串作为姓名
        return new PersonInfo(personStr.trim(), "", "");
    }

    /**
     * 解析多人信息字符串
     * 输入格式: "王强(运输管理处) 13907712002,李静(办公室主任) 13907712002,赵安全(安全监管处处长) 13907712002"
     * 输出: List<PersonInfo>
     *
     * @param membersStr 多人信息字符串，用逗号分隔
     * @return PersonInfo列表
     */
    private java.util.List<PersonInfo> parseMultiplePersonInfo(String membersStr) {
        java.util.List<PersonInfo> personList = new java.util.ArrayList<>();

        if (membersStr == null || membersStr.trim().isEmpty()) {
            return personList;
        }

        try {
            // 按逗号分割多个人员信息
            String[] personStrings = membersStr.split(",");

            for (String personStr : personStrings) {
                if (personStr != null && !personStr.trim().isEmpty()) {
                    PersonInfo person = parsePersonInfo(personStr.trim());
                    if (!person.getName().isEmpty()) {
                        personList.add(person);
                    }
                }
            }
        } catch (Exception e) {
            log.error("解析多人信息失败: " + membersStr, e);
        }

        return personList;
    }

    /**
     * 格式化单个人员信息
     * 输入: PersonInfo
     * 输出: "[李明华][自治区交通运输厅厅长，13907711001]"
     *
     * @param person 人员信息对象
     * @return 格式化后的人员信息字符串
     */
    private String formatPersonInfo(PersonInfo person) {
        if (person == null || person.getName().isEmpty()) {
            return "";
        }

        StringBuilder formatted = new StringBuilder();
        formatted.append("[").append(person.getName()).append("]");

        // 构建职务和联系方式部分
        StringBuilder details = new StringBuilder();
        if (!person.getPosition().isEmpty()) {
            details.append(person.getPosition());
        }
        if (!person.getPhone().isEmpty()) {
            if (details.length() > 0) {
                details.append("，");
            }
            details.append(person.getPhone());
        }

        if (details.length() > 0) {
            formatted.append("[").append(details.toString()).append("]");
        }

        return formatted.toString();
    }

    /**
     * 格式化人员信息（紧凑格式）
     * 输入: PersonInfo
     * 输出: "[王强][建设管理处长，13907712002]"
     *
     * @param person 人员信息对象
     * @return 格式化后的人员信息字符串（紧凑格式）
     */
    private String formatPersonInfoCompact(PersonInfo person) {
        if (person == null || person.getName().isEmpty()) {
            return "";
        }

        StringBuilder formatted = new StringBuilder();
        formatted.append("[").append(person.getName()).append("]");

        // 构建职务和联系方式部分
        StringBuilder details = new StringBuilder();
        if (!person.getPosition().isEmpty()) {
            details.append(person.getPosition());
        }
        if (!person.getPhone().isEmpty()) {
            if (details.length() > 0) {
                details.append("，");
            }
            details.append(person.getPhone());
        }

        if (details.length() > 0) {
            formatted.append("[").append(details.toString()).append("]");
        }

        return formatted.toString();
    }

    /**
     * 人员信息内部类
     */
    private static class PersonInfo {
        private String name;
        private String position;
        private String phone;

        public PersonInfo(String name, String position, String phone) {
            this.name = name != null ? name.trim() : "";
            this.position = position != null ? position.trim() : "";
            this.phone = phone != null ? phone.trim() : "";
        }

        public String getName() {
            return name;
        }

        public String getPosition() {
            return position;
        }

        public String getPhone() {
            return phone;
        }

        @Override
        public String toString() {
            return "PersonInfo{name='" + name + "', position='" + position + "', phone='" + phone + "'}";
        }
    }
}
