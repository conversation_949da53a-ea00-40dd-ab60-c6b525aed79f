{"msg": "�����ɹ�", "code": 200, "data": {"id": "d34e93a027fd44d9bca6be7f2fcf8e28", "compilingDept": "100", "version": "0.1", "planName": "����׳����������·��ͨͻ���¼�Ӧ��Ԥ��", "planType": "1", "applicableDeptIds": "100", "purpose": "string", "basis": "string", "scope": "��������Χ�ڢ򼶼����Ϲ�·��ͨͻ���¼�", "workPrinciple": "aaaaa", "preventiveMeasures": "1. �����ż�ǿ������Ԥ��Ԥ���������ֺ�������Ԥ�⡣\n2. ��ͨ������ǰ������ѩ�ڱ����ʣ����׽��·�ν����Ų�ͷ�����\n3. �������Ŷ����ܵ�����ѩӰ��ĵ�����ʩ����Ѳ��������ų���\n4. ��������֯��չ�����������֪ʶ��������ѵ��", "warningPrinciple": "1. �緢�֡��籨�桢��Ԥ�����紦�õ�\"����\"ԭ��\n2. ���ٷ�Ӧ��Э���������ּ���������Ϊ����ԭ��\n3. ����Ϊ������ѧԤ������׼���������ڲ����ԭ��\n4. ȷ��Ԥ����Ϣ������ʱ��׼ȷ��Ȩ�����ɿ���", "warningInfoCollect": "���󡢽�ͨ��ˮ���������Ȳ���ͨ�����ϵͳ�ռ��ֺ�����������Ϣ����ʱ����Ӧ������ֱ��ͣ����ۺ�Э������֯�������У�ΪԤ����Ϣ�����ṩ���ݡ�", "warningLevel": "����ѩ�ֿ�����ɵ�Σ���̶ȡ������̶Ⱥͷ�չ̬�ƣ�Ԥ�������ɸߵ��ͷ�Ϊ��ɫ����ɫ����ɫ����ɫ�ļ���\n\n1. ��ɫԤ����I������Ԥ��δ��24Сʱ�ڿ��ܳ��ִ�ѩ��24Сʱ��ѩ����15.0mm�������ѳ��ִ�ѩ�ҿ��ܳ��������ܻ��Ѿ�����ش�ͨ��ϡ���Χ����ͨ���жϵ�����Ӱ�졣\n\n2. ��ɫԤ����II������Ԥ��δ��24Сʱ�ڿ��ܳ��ֱ�ѩ��24Сʱ��ѩ��10.0��14.9mm�������ѳ��ֱ�ѩ�ҿ��ܳ��������ܻ��Ѿ���ɵ�·�������ͨ���������Ӱ�졣\n\n3. ��ɫԤ����III������Ԥ��δ��24Сʱ�ڿ��ܳ��ִ�ѩ��24Сʱ��ѩ��5.0��9.9mm�������ѳ��ִ�ѩ�ҿ��ܳ������Խ�ͨ���е��нϴ�Ӱ�졣\n\n4. ��ɫԤ����IV������Ԥ��δ��24Сʱ�ڿ��ܳ�����ѩ��24Сʱ��ѩ��2.5��4.9mm�������ѳ�����ѩ�ҿ��ܳ������Խ�ͨ���е���һ��Ӱ�졣", "warningPublish": "1. �������壺������ָ��𷢲������ֺ�Ԥ����Ϣ����Ӧ������ָ���ͳ��Э��Ԥ����Ϣ����������\n\n2. �������ݣ�����Ԥ������Ԥ������Ԥ����ʼʱ�䡢����Ӱ�췶Χ����ʾ���Ӧ�Դ�ʩ�����������ȡ�\n\n3. ����������\n   - ͨ�����ӡ��㲥����ֽ�����������ֻ����ŵȷ�ʽ����Ԥ����Ϣ\n   - ����������վ��������ý��ƽ̨����\n   - ʹ��Ԥ�������ȡ�������ʾ������ʩ\n   - ��������Ա�뻧��֪�ȷ�ʽ\n\n4. �������̣�������ַ����ֺ����������������С����Ԥ����Ϣ�����������Ӧ���������ˡ�����Ԥ����Ϣ", "warningMeasures": "1. ��ɫԤ����I������ʩ��\n   - ����24СʱӦ��ֵ��\n   - ֹͣһ�л��⼯�ᡢ�\n   - ͣ�Ρ�ͣ����ͣҵ\n   - ����Ӧ�����ʺͶ��鵽λ\n   - ��ǿѲ�飬��ʱ�ų�����\n\n2. ��ɫԤ����II������ʩ��\n   - ��ǿֵ��ֵ��\n   - ����ֹͣ���⼯��͸߿յ�Σ����ҵ\n   - ѧУֹͣ����\n   - ׼��Ӧ�����ʺͶ���\n\n3. ��ɫԤ����III������ʩ��\n   - ��ǿ���Ԥ��\n   - ��ز�������Ӧ��׼��\n   - ���Ѿ���ע�������ů\n\n4. ��ɫԤ����IV������ʩ��\n   - ���й�ע�����仯\n   - ע�������ů\n   - ���÷�������׼��", "eventLevel": "2", "responseCondition": "string", "processFlow": "1. ��������I����Ӧ���ƣ���������Ӧ������ָ�Ӳ���������ָ�ӳ���\n2. ��ʱ���ϼ����ű�������;��ֹ�����չ�����\n3. ��֯��ͨ��ͨҪ����ȷ�����ֳ���ͨ�У�\n4. ������ѩ����������Ҫ��·���г�ѩ��ҵ��\n5. ����ҽ�ƾȻ�������������Աת�ƺ;��ι�����\n6. ����������޶��飬���޵�����ʩ���ָ����磻\n7. ��֯ת�ư�������Ⱥ�ڣ����ϻ�����������", "infoReport": "����\"���١�׼ȷ��ȫ��\"��ԭ�򣬼�ʱ�ռ��ͱ���������Ϣ���漰��Ա�����ģ�Ӧ���·���30�����ڵ绰���棬1Сʱ�����汨�档I��II���¼���ÿ2Сʱ����һ�Σ�ֱ��Ӧ�����ù���������", "newsRelease": "���ռ�ʱ��׼ȷ���͹ۡ�ȫ���ԭ�������������Ű츺����֯Э�����ŷ����������ش�������Ϣ������Ӧ�������������ϼ����ܲ���ͬ�⡣", "responseAdjust": "�����ֺ���̬��չ����ʹ��ù���ʵ����Ҫ����ʱ����Ӧ����Ӧ���𡣵��ֺ��õ���Ч���ƣ������Ѿ����������ֵ����������ָ���������������Ӧ��ͬ������������ֹӦ����Ӧ��", "aftermathDisposal": "1. ��ʱ�ָ�ˮ���硢����ͨ�ŵȻ�����ʩ���ܣ��ָ�����������������\n2. ��������Ⱥ�ڣ���������Ⱥ�ڵĻ�������Ϲ��������ź�����Ⱥ����ʱס����\n3. ��֯��չ�������߹�������ֹ�����������顣\n4. ���������ʽ�����ʣ�Ѹ�ٵ������ִ������ʣ�֧Ԯ��������Ⱥ�����\n5. ������������Ա�������и�ο���Խ������������õ��������������涨���貹����", "summaryEvaluation": "1. �ֺ����ù�����������Ӧ��ָ�Ӳ���֯��չ�ֺ���������������ȫ������ֺ�������������ʧ�������Ԯ���̺�Ӧ������Ч����\n2. ����ص�λ���ֺ�Ӧ��������������ܽᣬ�������ڵ����⣬����Ľ����顣\n3. �γ��ֺ��¼������������棬����������ͬʱ������ز��š�\n4. �����ܽ������������һ������Ӧ��Ԥ������ǿӦ�����齨�裬�Ľ�Ӧ�����ϴ�ʩ��", "materialSupport": "1. ��Ӧ������ָ�����֯��������������Ӧ�����ʡ�\n2. ���ǹ�ί������֯������ѩ�������¡�ɨ��ȳ�ѩ���ʡ�\n3. ����ͨ�ָ����ѩ�豸�ͳ����Ĵ����͵��ȡ�\n4. ���ֵ����������ص㵥λ��ְ�𴢱���Ҫ��Ӧ��װ�������ʡ�", "communicationSupport": "1. ��ͨ�Ź���칫��Э����������Ӫ��ȷ��ͨ��ϵͳ�������С�\n2. ����Ӧ���㲥ϵͳ��������Ϣ��ʱ������\n3. �䱸Ӧ��ͨ���豸��ȷ���ؼ���λ��Աͨ�ų�ͨ��\n4. ���̶�ͨ����ʩ�⵽�ƻ�ʱ���������ǵ绰�ȱ���ͨ���ֶΡ�", "trafficSupport": "1. �������־ֽ�����Ӹ���ָ��������ͨ�����Ͼ�Ԯ��·��ͨ��\n2. ����ͨ�ָ���Э��������ͨ���ߣ�����Ⱥ����ɢ�;�Ԯ�������䡣\n3. ��������£���������ᳵ���������վ��֡�\n4. ����Ӧ���������Ȼ��ƣ�ȷ��Ӧ����������ͨ�С�", "fundingSupport": "���ѱ���", "planRevision": "string", "publicityTraining": "1. Ӧ������ָ�����֯��Ӧ��Ԥ��������������ѵ������\n2. ����ص�λӦ����֯�����š�����λ����ҵ��Ӧ��������Աѧϰ��Ԥ����\n3. ������<PERSON>㲥�����ӡ���ֽ����������ý�壬��ǿ�Թ��ڵ�����������\n4. ������֯ר�ҽ�ѧУ��������������ҵ��չ���ּ���֪ʶ�������", "planDrill": "1. ������ʽ���������ݡ������������ۺ������ȡ�\n\n2. ����Ƶ�Σ���Ӧ�������Ӧ��ÿ��������֯һ��Ӧ������������ص�λ���ݸ���ְ���мƻ�����֯�����Ա����ר��Ӧ��Ԥ��������\n\n3. ����Ҫ������ǰ�ƶ����ܵ���������������������������������Ч��������������ʱ�����ܽᣬ�������⣬����Ľ����顣\n\n4. �������ݣ�Ԥ����Ϣ������Ӧ����Ӧ��ָ��Э������Ա��ɢ�����ʵ��䡢Ӧ��ͨ�ŵȡ�", "implementTime": "��Ԥ���Է���֮����ʵʩ��", "planStatus": 0, "enableStatus": 0, "checkStatus": 0, "lastCheckTime": "2019-08-24", "creator": "admin", "updater": null, "reviser": "string", "revisionTime": null, "revisionContent": "string", "delFlag": 0, "levelDTOList": [{"createBy": null, "createTime": "2025-06-03 15:23:45", "updateBy": null, "updateTime": "2025-06-04 08:39:02", "remark": null, "id": null, "prePlanId": "d34e93a027fd44d9bca6be7f2fcf8e28", "version": "0.1", "eventLevel": 1, "conditions": "ȫ����Χ�ڳ��ִ�Χ������ǿ��ѩ�����¡���·������ֺ�����ɽ�ͨ��������ͨ�ŵȻ�����ʩ�������٣�������ش���Ա������", "processFlow": "1.�����ش�ѩ��Ӧ����Ӧ������Ӧ��ָ�Ӳ�.\n2.��֯����רҵ��Ԯ����ϸ�������������չ��Ԯ\n3.��ǿ��ͨ�赼����ʱ������Ҫ��·��ѩ��\n4.���ȱ�����Ҫ����������ͨ�ų�ͨ,\n5.��������Ⱥ�ڻ�������Ϲ�����", "creator": "admin", "updater": "admin", "delFlag": 0}, {"createBy": null, "createTime": "2025-06-03 15:23:45", "updateBy": null, "updateTime": "2025-06-04 08:39:02", "remark": null, "id": null, "prePlanId": "d34e93a027fd44d9bca6be7f2fcf8e28", "version": "0.1", "eventLevel": 2, "conditions": "������ǿ��ѩ�����¡������ֺ�����ɽ�ͨ��ϡ������жϵ�Ӱ�죬���ֵ����������ء�", "processFlow": "1.�����ش�ѩ��Ӧ����Ӧ������Ӧ��ָ�Ӳ�.\n2.��֯����רҵ��Ԯ����ϸ�������������չ��Ԯ\n3.��ǿ��ͨ�赼����ʱ������Ҫ��·��ѩ��\n4.���ȱ�����Ҫ����������ͨ�ų�ͨ,\n5.��������Ⱥ�ڻ�������Ϲ�����", "creator": "admin", "updater": "admin", "delFlag": 0}], "emPrePlanDeptDTOList": [{"id": "8f85e3fd-55c7-4c02-bb09-1014e6591ecf", "orderNum": null, "parentId": null, "version": "0.1", "prePlanId": "d34e93a027fd44d9bca6be7f2fcf8e28", "deptName": "������Ӧ��ָ�ӻ���", "deptLevel": 0, "deptJob": null, "eventLevel": null, "creator": "admin", "updater": "admin", "delFlag": 0, "leader": "", "leaderAss": "", "member": null, "pro": null, "children": [{"id": "befa18fe-5590-4be8-81bf-d405c561189a", "orderNum": null, "parentId": "8f85e3fd-55c7-4c02-bb09-1014e6591ecf", "version": "0.1", "prePlanId": "d34e93a027fd44d9bca6be7f2fcf8e28", "deptName": "�쵼С��", "deptLevel": 1, "deptJob": "string", "eventLevel": null, "creator": "admin", "updater": "admin", "delFlag": 0, "leader": "������(��������ͨ����������) 13907711001", "leaderAss": "�Ž���(��������ͨ������������) 13907712002", "member": "��ǿ(���������) 13907712002,�¾�(�칫������) 13907712002,����(��ȫ�ල������) 13907712002", "pro": null, "children": [], "emPrePlanDeptUserDTOList": null}, {"id": "fbf61342-947f-4a66-ba70-bf4137e7bfd4", "orderNum": null, "parentId": "8f85e3fd-55c7-4c02-bb09-1014e6591ecf", "version": "0.1", "prePlanId": "d34e93a027fd44d9bca6be7f2fcf8e28", "deptName": "�쵼С��칫��", "deptLevel": 1, "deptJob": "string", "eventLevel": null, "creator": "admin", "updater": "admin", "delFlag": 0, "leader": "�԰�ȫ(����ȫ�ܼ�) 3907714001", "leaderAss": "��ǿ(���������) 13907712002", "member": "���(���������) 13907712002,�ܰ칫(�칫������) 13907714003,�Ű���(��ȫ�ල��) 13907714003", "pro": null, "children": [], "emPrePlanDeptUserDTOList": null}, {"id": "ebb5601a-b86e-4e33-94c5-db8a82607fce", "orderNum": null, "parentId": "8f85e3fd-55c7-4c02-bb09-1014e6591ecf", "version": "0.1", "prePlanId": "d34e93a027fd44d9bca6be7f2fcf8e28", "deptName": "Ӧ��������", "deptLevel": 1, "deptJob": "string", "eventLevel": null, "creator": "admin", "updater": "admin", "delFlag": 0, "leader": "", "leaderAss": "", "member": null, "pro": null, "children": [{"id": "43919a0a-78f5-4ca1-9636-7da6e5135758", "orderNum": null, "parentId": "ebb5601a-b86e-4e33-94c5-db8a82607fce", "version": "0.1", "prePlanId": "d34e93a027fd44d9bca6be7f2fcf8e28", "deptName": "�ۺ�Э����", "deptLevel": 1, "deptJob": null, "eventLevel": null, "creator": "admin", "updater": "admin", "delFlag": 0, "leader": "�¾�(���칫������) 13907714003", "leaderAss": "��ǿ(���������) 13907714003", "member": null, "pro": null, "children": [], "emPrePlanDeptUserDTOList": null}, {"id": "de7ce2c1-559e-4d92-affd-b43abb15c3e8", "orderNum": null, "parentId": "ebb5601a-b86e-4e33-94c5-db8a82607fce", "version": "0.1", "prePlanId": "d34e93a027fd44d9bca6be7f2fcf8e28", "deptName": "Ӧ��ָ����", "deptLevel": 1, "deptJob": null, "eventLevel": null, "creator": "admin", "updater": "admin", "delFlag": 0, "leader": "��ǿ(���������) 13907714003", "leaderAss": "����·(��������·��չ���ĸ�����) 13907716001", "member": null, "pro": null, "children": [], "emPrePlanDeptUserDTOList": null}, {"id": "af0a951d-80ea-44dc-96ea-5e90305bbdba", "orderNum": null, "parentId": "ebb5601a-b86e-4e33-94c5-db8a82607fce", "version": "0.1", "prePlanId": "d34e93a027fd44d9bca6be7f2fcf8e28", "deptName": "���䱣����", "deptLevel": 1, "deptJob": null, "eventLevel": null, "creator": "admin", "updater": "admin", "delFlag": 0, "leader": "������(���������) 13907716004", "leaderAss": "���·(��������·��չ���ĸ�����) 13907716001", "member": null, "pro": null, "children": [], "emPrePlanDeptUserDTOList": null}, {"id": "655fe773-374f-4751-9bcf-771d1b3f3e1e", "orderNum": null, "parentId": "ebb5601a-b86e-4e33-94c5-db8a82607fce", "version": "0.1", "prePlanId": "d34e93a027fd44d9bca6be7f2fcf8e28", "deptName": "����������", "deptLevel": 1, "deptJob": null, "eventLevel": null, "creator": "admin", "updater": "admin", "delFlag": 0, "leader": "֣��ί(���ص�ίרְ�����) 13907716004", "leaderAss": "�¾�(�칫������) 13907716001", "member": null, "pro": null, "children": [], "emPrePlanDeptUserDTOList": null}, {"id": "181243db-02ea-458e-aa6f-28be3fdb81b2", "orderNum": null, "parentId": "ebb5601a-b86e-4e33-94c5-db8a82607fce", "version": "0.1", "prePlanId": "d34e93a027fd44d9bca6be7f2fcf8e28", "deptName": "ͨ�ű�����", "deptLevel": 1, "deptJob": null, "eventLevel": null, "creator": "admin", "updater": "admin", "delFlag": 0, "leader": "���ƽ�(�ƽ̴�������) 13907716004", "leaderAss": "����·(��������·��չ���ķֹ��쵼) 13907716001", "member": null, "pro": null, "children": [], "emPrePlanDeptUserDTOList": null}, {"id": "7f5692ba-2c44-4291-b4ad-9fd991b0f2d2", "orderNum": null, "parentId": "ebb5601a-b86e-4e33-94c5-db8a82607fce", "version": "0.1", "prePlanId": "d34e93a027fd44d9bca6be7f2fcf8e28", "deptName": "���ڱ�����", "deptLevel": 1, "deptJob": null, "eventLevel": null, "creator": "admin", "updater": "admin", "delFlag": 0, "leader": "������(���ط������ĸ�����) 13907716004", "leaderAss": "������(���񴦸���) 13907716001", "member": null, "pro": null, "children": [], "emPrePlanDeptUserDTOList": null}], "emPrePlanDeptUserDTOList": null}, {"id": "2c46c08b-6850-48d8-86de-7d65a8f90137", "orderNum": null, "parentId": "8f85e3fd-55c7-4c02-bb09-1014e6591ecf", "version": "0.1", "prePlanId": "d34e93a027fd44d9bca6be7f2fcf8e28", "deptName": "�ֳ�������", "deptLevel": 1, "deptJob": null, "eventLevel": null, "creator": "admin", "updater": "admin", "delFlag": 0, "leader": "", "leaderAss": "", "member": "���ֳ�(�ֳ�Э��Ա) 13907716004,�缼��(רҵ������Ա) 13907716004,�װ�ȫ(��ȫ�ලԱ) 13907721004,��ר��(����ר��) 13907716004", "pro": null, "children": [], "emPrePlanDeptUserDTOList": null}, {"id": "5db13875-dfd0-499c-b445-b2bc82dd405b", "orderNum": null, "parentId": "8f85e3fd-55c7-4c02-bb09-1014e6591ecf", "version": "0.1", "prePlanId": "d34e93a027fd44d9bca6be7f2fcf8e28", "deptName": "ר����", "deptLevel": 1, "deptJob": null, "eventLevel": null, "creator": "admin", "updater": "admin", "delFlag": 0, "leader": "", "leaderAss": "", "member": null, "pro": "�轨��(�������ר��,������ѧ����) 13907721004", "children": [], "emPrePlanDeptUserDTOList": null}], "emPrePlanDeptUserDTOList": null}, {"id": "604f2aa7-06e5-46c3-b4ad-508b1ab9563f", "orderNum": null, "parentId": null, "version": "0.1", "prePlanId": "d34e93a027fd44d9bca6be7f2fcf8e28", "deptName": "�С��ؼ�Ӧ��ָ�ӻ���", "deptLevel": 2, "deptJob": null, "eventLevel": null, "creator": "admin", "updater": "admin", "delFlag": 0, "leader": "", "leaderAss": "", "member": null, "pro": null, "children": [{"id": "2bc77f03-96cd-4f60-81fb-4422b6503d86", "orderNum": null, "parentId": "604f2aa7-06e5-46c3-b4ad-508b1ab9563f", "version": "0.1", "prePlanId": "d34e93a027fd44d9bca6be7f2fcf8e28", "deptName": "������Ӧ��ָ�ӻ���", "deptLevel": 1, "deptJob": null, "eventLevel": null, "creator": "admin", "updater": "admin", "delFlag": 0, "leader": "���г�(�����н�ͨ����־ֳ�) 13907721004", "leaderAss": "�¸���(��������ͨ����ָ��ֳ�) 13907721004Ī����(����ƿƳ�) 13907721004", "member": "¬��ȫ(��ȫ�ƿƳ�) 13907721004,¬��ȫ(�����й�·��������) 13907721004", "pro": null, "children": [], "emPrePlanDeptUserDTOList": null}, {"id": "c024d9bd-5c61-48a4-8a5f-ffd8b81c96c6", "orderNum": null, "parentId": "604f2aa7-06e5-46c3-b4ad-508b1ab9563f", "version": "0.1", "prePlanId": "d34e93a027fd44d9bca6be7f2fcf8e28", "deptName": "������Ӧ��ָ�ӻ���", "deptLevel": 1, "deptJob": null, "eventLevel": null, "creator": "admin", "updater": "admin", "delFlag": 0, "leader": "������(�����н�ͨ����־ֳ�) 13907721004", "leaderAss": "ũ����(��������ͨ����ָ��ֳ�) 13907721004", "member": "ʯ����(�����й�·��������) 13907721004,����ȫ(�����й�·��������) 13907721004,�๫·(�����й�·��������) 13907721004", "pro": null, "children": [], "emPrePlanDeptUserDTOList": null}], "emPrePlanDeptUserDTOList": null}], "emMeasureDTOList": [{"id": null, "version": "0.1", "prePlanId": "d34e93a027fd44d9bca6be7f2fcf8e28", "eventLevel": 0, "triggerCondition": "string", "measureContent": "string", "isDispatchTeam": 0, "needReport": 0, "resourceTypes": "string", "specialOperations": "string", "creator": "admin", "updater": "string", "isDeleted": null}], "emPrePlanFileDTOS": [{"id": "8780eb51fac649dea4fa07393fdbd5fe", "version": "0.1", "bizId": "d34e93a027fd44d9bca6be7f2fcf8e28", "fileName": "string", "fileType": "string", "fileDesc": "string", "creator": "admin", "updater": "string", "delFlag": 0, "url": "string", "fileSize": 0, "originalFileName": "string", "newFileName": "string"}]}}